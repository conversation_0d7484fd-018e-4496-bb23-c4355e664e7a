<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VTable Custom Dropdown Editor</title>
    <script src="https://unpkg.com/@visactor/vtable/dist/vtable.min.js"></script>
    <style>
        body {
            margin: 20px;
            font-family: Arial, sans-serif;
        }

        #tableContainer {
            width: 800px;
            height: 500px;
            border: 1px solid #ccc;
        }

        /* 自定义下拉框样式 */
        .custom-dropdown {
            position: absolute;
            width: 100%;
            height: 100%;
            z-index: 1000;
        }

        .dropdown-input {
            width: 100%;
            height: 100%;
            border: none;
            outline: none;
            cursor: pointer;
            background: white;
            padding: 0 20px 0 8px;
            box-sizing: border-box;
        }

        .dropdown-arrow {
            position: absolute;
            right: 5px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            font-size: 12px;
            color: #666;
        }

        .dropdown-list {
            position: absolute;
            top: 100%;
            left: 0;
            width: 100%;
            max-height: 150px;
            overflow-y: auto;
            background: white;
            border: 1px solid #ccc;
            border-top: none;
            display: none;
            z-index: 1001;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .dropdown-option {
            padding: 8px 12px;
            cursor: pointer;
            border-bottom: 1px solid #eee;
            transition: background-color 0.2s;
        }

        .dropdown-option:hover {
            background-color: #f0f0f0;
        }

        .dropdown-option:last-child {
            border-bottom: none;
        }

        .dropdown-option.selected {
            background-color: #e6f7ff;
            color: #1890ff;
        }
    </style>
</head>

<body>
    <h1>VTable 自定义下拉框编辑器示例</h1>
    <p>双击 "状态" 列的单元格来编辑，下拉框会在单元格内展示，滚动时自动消失</p>
    <div id="tableContainer"></div>

    <script>
        // 自定义下拉框编辑器类
        class CustomDropdownEditor {
            constructor(editorConfig = {}) {
                this.editorConfig = editorConfig;
                this.container = null;
                this.element = null;
                this.dropdown = null;
                this.successCallback = null;
                this.currentValue = '';
                this.options = editorConfig.values || ['选项1', '选项2', '选项3'];
            }

            onStart({ container, value, referencePosition, endEdit }) {
                this.container = container;
                this.successCallback = endEdit;
                this.currentValue = value || '';

                // 创建下拉框容器
                const wrapper = document.createElement('div');
                wrapper.className = 'custom-dropdown';

                // 创建显示当前值的输入框
                const input = document.createElement('input');
                input.className = 'dropdown-input';
                input.type = 'text';
                input.value = this.currentValue;
                input.readOnly = true;

                // 创建下拉箭头
                const arrow = document.createElement('span');
                arrow.className = 'dropdown-arrow';
                arrow.innerHTML = '▼';

                // 创建下拉选项容器
                const dropdown = document.createElement('div');
                dropdown.className = 'dropdown-list';

                // 添加选项
                this.options.forEach(optionValue => {
                    const option = document.createElement('div');
                    option.className = 'dropdown-option';
                    option.textContent = optionValue;

                    // 标记当前选中的选项
                    if (optionValue === this.currentValue) {
                        option.classList.add('selected');
                    }

                    // 点击选项事件
                    option.addEventListener('click', (e) => {
                        e.stopPropagation();
                        input.value = optionValue;
                        this.currentValue = optionValue;
                        dropdown.style.display = 'none';

                        // 更新选中状态
                        dropdown.querySelectorAll('.dropdown-option').forEach(opt => {
                            opt.classList.remove('selected');
                        });
                        option.classList.add('selected');

                        // 结束编辑
                        setTimeout(() => {
                            this.successCallback();
                        }, 100);
                    });

                    dropdown.appendChild(option);
                });

                // 点击输入框或箭头显示/隐藏下拉框
                const toggleDropdown = (e) => {
                    e.stopPropagation();
                    const isVisible = dropdown.style.display === 'block';
                    dropdown.style.display = isVisible ? 'none' : 'block';
                };

                input.addEventListener('click', toggleDropdown);
                arrow.addEventListener('click', toggleDropdown);

                // 组装元素
                wrapper.appendChild(input);
                wrapper.appendChild(arrow);
                wrapper.appendChild(dropdown);

                this.element = wrapper;
                this.dropdown = dropdown;

                container.appendChild(wrapper);

                // 设置位置
                if (referencePosition?.rect) {
                    this.adjustPosition(referencePosition.rect);
                }

                // 监听滚动事件，滚动时隐藏下拉框
                this.handleScroll = () => {
                    if (this.dropdown) {
                        this.dropdown.style.display = 'none';
                    }
                };

                // 监听点击外部区域关闭下拉框
                this.handleClickOutside = (e) => {
                    if (this.element && !this.element.contains(e.target)) {
                        this.dropdown.style.display = 'none';
                    }
                };

                // 添加事件监听
                document.addEventListener('scroll', this.handleScroll, true);
                document.addEventListener('click', this.handleClickOutside);

                // 自动显示下拉框
                setTimeout(() => {
                    dropdown.style.display = 'block';
                }, 50);
            }

            adjustPosition(rect) {
                if (this.element) {
                    this.element.style.top = rect.top + 'px';
                    this.element.style.left = rect.left + 'px';
                    this.element.style.width = rect.width + 'px';
                    this.element.style.height = rect.height + 'px';
                }
            }

            getValue() {
                return this.currentValue;
            }

            onEnd() {
                // 移除事件监听
                document.removeEventListener('scroll', this.handleScroll, true);
                document.removeEventListener('click', this.handleClickOutside);

                // 移除DOM元素
                if (this.element && this.container && this.container.contains(this.element)) {
                    this.container.removeChild(this.element);
                }
            }

            isEditorElement(target) {
                return this.element && this.element.contains(target);
            }
        }

        // 创建不同的下拉框编辑器实例
        const statusEditor = new CustomDropdownEditor({
            values: ['待处理', '进行中', '已完成', '已取消']
        });

        const priorityEditor = new CustomDropdownEditor({
            values: ['低', '中', '高', '紧急']
        });

        const categoryEditor = new CustomDropdownEditor({
            values: ['开发', '测试', '设计', '产品', '运营']
        });

        // 注册编辑器
        VTable.register.editor('status-editor', statusEditor);
        VTable.register.editor('priority-editor', priorityEditor);
        VTable.register.editor('category-editor', categoryEditor);

        // 配置表格数据
        const data = [
            { id: 1, name: '任务一', status: '待处理', priority: '高', category: '开发', description: '这是第一个任务' },
            { id: 2, name: '任务二', status: '进行中', priority: '中', category: '测试', description: '这是第二个任务' },
            { id: 3, name: '任务三', status: '已完成', priority: '低', category: '设计', description: '这是第三个任务' },
            { id: 4, name: '任务四', status: '已取消', priority: '紧急', category: '产品', description: '这是第四个任务' },
            { id: 5, name: '任务五', status: '待处理', priority: '中', category: '运营', description: '这是第五个任务' }
        ];

        // 配置表格列
        const columns = [
            {
                field: 'id',
                title: 'ID',
                width: 60
            },
            {
                field: 'name',
                title: '任务名称',
                width: 120
            },
            {
                field: 'status',
                title: '状态',
                width: 100,
                editor: 'status-editor'
            },
            {
                field: 'priority',
                title: '优先级',
                width: 80,
                editor: 'priority-editor'
            },
            {
                field: 'category',
                title: '分类',
                width: 80,
                editor: 'category-editor'
            },
            {
                field: 'description',
                title: '描述',
                width: 200
            }
        ];

        // 初始化 VTable
        const tableInstance = new VTable.ListTable({
            container: document.getElementById('tableContainer'),
            records: data,
            columns: columns,
            editCellTrigger: 'doubleclick', // 双击触发编辑
            theme: VTable.themes.DEFAULT.extends({
                headerStyle: {
                    bgColor: '#f8f9fa',
                    color: '#333'
                },
                bodyStyle: {
                    hover: {
                        cellBgColor: '#f5f5f5'
                    }
                }
            })
        });

        // 监听编辑事件
        tableInstance.on('change_cell_value', (args) => {
            console.log('单元格值已更改:', args);
        });

        // 添加一些说明文字
        const info = document.createElement('div');
        info.style.marginTop = '20px';
        info.style.padding = '10px';
        info.style.backgroundColor = '#f0f8ff';
        info.style.border = '1px solid #d0e7ff';
        info.style.borderRadius = '4px';
        info.innerHTML = `
            <h3>功能说明：</h3>
            <ul>
                <li>双击 "状态"、"优先级"、"分类" 列的单元格进入编辑模式</li>
                <li>下拉框会在单元格内展示，点击选项完成选择</li>
                <li>滚动页面时下拉框会自动消失</li>
                <li>点击其他区域也会关闭下拉框</li>
                <li>支持多个不同的下拉框编辑器</li>
            </ul>
        `;
        document.body.appendChild(info);
    </script>
</body>

</html>