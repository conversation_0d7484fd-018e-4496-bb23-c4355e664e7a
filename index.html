<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VTable Custom Dropdown Editor</title>
    <script src="https://unpkg.com/@visactor/vtable/dist/vtable.min.js"></script>
    <style>
        body {
            margin: 20px;
            font-family: Arial, sans-serif;
        }

        #tableContainer {
            width: 800px;
            height: 500px;
            border: 1px solid #ccc;
        }

        /* 自定义下拉框样式 */
        .custom-dropdown {
            position: absolute;
            width: 100%;
            height: 100%;
            z-index: 1000;
        }

        .dropdown-input {
            width: 100%;
            height: 100%;
            border: none;
            outline: none;
            cursor: pointer;
            background: white;
            padding: 0 20px 0 8px;
            box-sizing: border-box;
        }

        .dropdown-arrow {
            position: absolute;
            right: 5px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            font-size: 12px;
            color: #666;
        }

        .dropdown-list {
            position: absolute;
            top: 100%;
            left: 0;
            width: 100%;
            max-height: 150px;
            overflow-y: auto;
            background: white;
            border: 1px solid #ccc;
            border-top: none;
            display: none;
            z-index: 1001;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .dropdown-option {
            padding: 8px 12px;
            cursor: pointer;
            border-bottom: 1px solid #eee;
            transition: background-color 0.2s;
        }

        .dropdown-option:hover {
            background-color: #f0f0f0;
        }

        .dropdown-option:last-child {
            border-bottom: none;
        }

        .dropdown-option.selected {
            background-color: #e6f7ff;
            color: #1890ff;
        }

        /* 复选框编辑器样式 */
        .checkbox-editor {
            position: absolute;
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;
            z-index: 1000;
        }

        .checkbox-editor input[type="checkbox"] {
            width: 18px;
            height: 18px;
            cursor: pointer;
        }

        /* 日期时间选择器样式 */
        .datetime-editor {
            position: absolute;
            width: 100%;
            height: 100%;
            z-index: 1000;
        }

        .datetime-input {
            width: 100%;
            height: 100%;
            border: none;
            outline: none;
            padding: 0 8px;
            box-sizing: border-box;
            background: white;
        }

        /* 多选框编辑器样式 */
        .multiselect-editor {
            position: absolute;
            width: 100%;
            height: 100%;
            z-index: 1000;
        }

        .multiselect-input {
            width: 100%;
            height: 100%;
            border: none;
            outline: none;
            cursor: pointer;
            background: white;
            padding: 0 20px 0 8px;
            box-sizing: border-box;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .multiselect-list {
            position: absolute;
            top: 100%;
            left: 0;
            width: 100%;
            max-height: 200px;
            overflow-y: auto;
            background: white;
            border: 1px solid #ccc;
            border-top: none;
            display: none;
            z-index: 1001;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .multiselect-option {
            padding: 8px 12px;
            cursor: pointer;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
            transition: background-color 0.2s;
        }

        .multiselect-option:hover {
            background-color: #f0f0f0;
        }

        .multiselect-option:last-child {
            border-bottom: none;
        }

        .multiselect-option input[type="checkbox"] {
            margin-right: 8px;
        }

        .multiselect-option.selected {
            background-color: #e6f7ff;
        }

        .multiselect-tags {
            display: inline-block;
            background: #f0f0f0;
            padding: 2px 6px;
            margin: 1px;
            border-radius: 3px;
            font-size: 12px;
        }
    </style>
</head>

<body>
    <h1>VTable 多种自定义编辑器示例</h1>
    <p>双击各列的单元格来体验不同的编辑器：下拉框、复选框、日期时间选择器、多选框</p>
    <div id="tableContainer"></div>

    <script>
        // 复选框编辑器类
        class CheckboxEditor {
            constructor(editorConfig = {}) {
                this.editorConfig = editorConfig;
                this.container = null;
                this.element = null;
                this.successCallback = null;
                this.currentValue = false;
            }

            onStart({ container, value, referencePosition, endEdit }) {
                this.container = container;
                this.successCallback = endEdit;
                this.currentValue = value === true || value === 'true' || value === '是' || value === 1;

                // 创建复选框容器
                const wrapper = document.createElement('div');
                wrapper.className = 'checkbox-editor';

                // 创建复选框
                const checkbox = document.createElement('input');
                checkbox.type = 'checkbox';
                checkbox.checked = this.currentValue;

                // 复选框变化事件
                checkbox.addEventListener('change', (e) => {
                    this.currentValue = e.target.checked;
                    // 延迟一点时间让用户看到变化
                    setTimeout(() => {
                        this.successCallback();
                    }, 200);
                });

                wrapper.appendChild(checkbox);
                this.element = wrapper;

                container.appendChild(wrapper);

                // 设置位置
                if (referencePosition?.rect) {
                    this.adjustPosition(referencePosition.rect);
                }

                // 自动聚焦
                setTimeout(() => {
                    checkbox.focus();
                }, 50);
            }

            adjustPosition(rect) {
                if (this.element) {
                    this.element.style.top = rect.top + 'px';
                    this.element.style.left = rect.left + 'px';
                    this.element.style.width = rect.width + 'px';
                    this.element.style.height = rect.height + 'px';
                }
            }

            getValue() {
                return this.currentValue ? '是' : '否';
            }

            onEnd() {
                if (this.element && this.container && this.container.contains(this.element)) {
                    this.container.removeChild(this.element);
                }
            }

            isEditorElement(target) {
                return this.element && this.element.contains(target);
            }
        }

        // 日期时间选择器编辑器类
        class DateTimeEditor {
            constructor(editorConfig = {}) {
                this.editorConfig = editorConfig;
                this.container = null;
                this.element = null;
                this.successCallback = null;
                this.currentValue = '';
                this.type = editorConfig.type || 'datetime-local'; // 支持 date, time, datetime-local
            }

            onStart({ container, value, referencePosition, endEdit }) {
                this.container = container;
                this.successCallback = endEdit;
                this.currentValue = this.formatValue(value);

                // 创建日期时间选择器容器
                const wrapper = document.createElement('div');
                wrapper.className = 'datetime-editor';

                // 创建日期时间输入框
                const input = document.createElement('input');
                input.className = 'datetime-input';
                input.type = this.type;
                input.value = this.currentValue;

                // 值变化事件
                input.addEventListener('change', (e) => {
                    this.currentValue = e.target.value;
                    this.successCallback();
                });

                // 失去焦点时结束编辑
                input.addEventListener('blur', () => {
                    setTimeout(() => {
                        this.successCallback();
                    }, 100);
                });

                wrapper.appendChild(input);
                this.element = wrapper;

                container.appendChild(wrapper);

                // 设置位置
                if (referencePosition?.rect) {
                    this.adjustPosition(referencePosition.rect);
                }

                // 自动聚焦并打开选择器
                setTimeout(() => {
                    input.focus();
                    if (input.showPicker) {
                        input.showPicker();
                    }
                }, 50);
            }

            formatValue(value) {
                if (!value) return '';

                try {
                    const date = new Date(value);
                    if (isNaN(date.getTime())) return '';

                    if (this.type === 'date') {
                        return date.toISOString().split('T')[0];
                    } else if (this.type === 'time') {
                        return date.toTimeString().split(' ')[0].substring(0, 5);
                    } else {
                        // datetime-local
                        const offset = date.getTimezoneOffset();
                        const localDate = new Date(date.getTime() - (offset * 60 * 1000));
                        return localDate.toISOString().slice(0, 16);
                    }
                } catch (e) {
                    return '';
                }
            }

            adjustPosition(rect) {
                if (this.element) {
                    this.element.style.top = rect.top + 'px';
                    this.element.style.left = rect.left + 'px';
                    this.element.style.width = rect.width + 'px';
                    this.element.style.height = rect.height + 'px';
                }
            }

            getValue() {
                if (!this.currentValue) return '';

                try {
                    const date = new Date(this.currentValue);
                    if (isNaN(date.getTime())) return this.currentValue;

                    if (this.type === 'date') {
                        return date.toLocaleDateString('zh-CN');
                    } else if (this.type === 'time') {
                        return this.currentValue;
                    } else {
                        return date.toLocaleString('zh-CN');
                    }
                } catch (e) {
                    return this.currentValue;
                }
            }

            onEnd() {
                if (this.element && this.container && this.container.contains(this.element)) {
                    this.container.removeChild(this.element);
                }
            }

            isEditorElement(target) {
                return this.element && this.element.contains(target);
            }
        }

        // 自定义下拉框编辑器类
        class CustomDropdownEditor {
            constructor(editorConfig = {}) {
                this.editorConfig = editorConfig;
                this.container = null;
                this.element = null;
                this.dropdown = null;
                this.successCallback = null;
                this.currentValue = '';
                this.options = editorConfig.values || ['选项1', '选项2', '选项3'];
            }

            onStart({ container, value, referencePosition, endEdit }) {
                this.container = container;
                this.successCallback = endEdit;
                this.currentValue = value || '';

                // 创建下拉框容器
                const wrapper = document.createElement('div');
                wrapper.className = 'custom-dropdown';

                // 创建显示当前值的输入框
                const input = document.createElement('input');
                input.className = 'dropdown-input';
                input.type = 'text';
                input.value = this.currentValue;
                input.readOnly = true;

                // 创建下拉箭头
                const arrow = document.createElement('span');
                arrow.className = 'dropdown-arrow';
                arrow.innerHTML = '▼';

                // 创建下拉选项容器
                const dropdown = document.createElement('div');
                dropdown.className = 'dropdown-list';

                // 添加选项
                this.options.forEach(optionValue => {
                    const option = document.createElement('div');
                    option.className = 'dropdown-option';
                    option.textContent = optionValue;

                    // 标记当前选中的选项
                    if (optionValue === this.currentValue) {
                        option.classList.add('selected');
                    }

                    // 点击选项事件
                    option.addEventListener('click', (e) => {
                        e.stopPropagation();
                        input.value = optionValue;
                        this.currentValue = optionValue;
                        dropdown.style.display = 'none';

                        // 更新选中状态
                        dropdown.querySelectorAll('.dropdown-option').forEach(opt => {
                            opt.classList.remove('selected');
                        });
                        option.classList.add('selected');

                        // 结束编辑
                        setTimeout(() => {
                            this.successCallback();
                        }, 100);
                    });

                    dropdown.appendChild(option);
                });

                // 点击输入框或箭头显示/隐藏下拉框
                const toggleDropdown = (e) => {
                    e.stopPropagation();
                    const isVisible = dropdown.style.display === 'block';
                    dropdown.style.display = isVisible ? 'none' : 'block';
                };

                input.addEventListener('click', toggleDropdown);
                arrow.addEventListener('click', toggleDropdown);

                // 组装元素
                wrapper.appendChild(input);
                wrapper.appendChild(arrow);
                wrapper.appendChild(dropdown);

                this.element = wrapper;
                this.dropdown = dropdown;

                container.appendChild(wrapper);

                // 设置位置
                if (referencePosition?.rect) {
                    this.adjustPosition(referencePosition.rect);
                }

                // 监听滚动事件，滚动时隐藏下拉框
                this.handleScroll = () => {
                    if (this.dropdown) {
                        this.dropdown.style.display = 'none';
                    }
                };

                // 监听点击外部区域关闭下拉框
                this.handleClickOutside = (e) => {
                    if (this.element && !this.element.contains(e.target)) {
                        this.dropdown.style.display = 'none';
                    }
                };

                // 添加事件监听
                document.addEventListener('scroll', this.handleScroll, true);
                document.addEventListener('click', this.handleClickOutside);

                // 自动显示下拉框
                setTimeout(() => {
                    dropdown.style.display = 'block';
                }, 50);
            }

            adjustPosition(rect) {
                if (this.element) {
                    this.element.style.top = rect.top + 'px';
                    this.element.style.left = rect.left + 'px';
                    this.element.style.width = rect.width + 'px';
                    this.element.style.height = rect.height + 'px';
                }
            }

            getValue() {
                return this.currentValue;
            }

            onEnd() {
                // 移除事件监听
                document.removeEventListener('scroll', this.handleScroll, true);
                document.removeEventListener('click', this.handleClickOutside);

                // 移除DOM元素
                if (this.element && this.container && this.container.contains(this.element)) {
                    this.container.removeChild(this.element);
                }
            }

            isEditorElement(target) {
                return this.element && this.element.contains(target);
            }
        }

        // 多选框编辑器类
        class MultiSelectEditor {
            constructor(editorConfig = {}) {
                this.editorConfig = editorConfig;
                this.container = null;
                this.element = null;
                this.dropdown = null;
                this.successCallback = null;
                this.selectedValues = [];
                this.options = editorConfig.values || ['选项1', '选项2', '选项3'];
            }

            onStart({ container, value, referencePosition, endEdit }) {
                this.container = container;
                this.successCallback = endEdit;
                this.selectedValues = this.parseValue(value);

                // 创建多选框容器
                const wrapper = document.createElement('div');
                wrapper.className = 'multiselect-editor';

                // 创建显示当前值的输入框
                const input = document.createElement('input');
                input.className = 'multiselect-input';
                input.type = 'text';
                input.value = this.formatDisplayValue();
                input.readOnly = true;

                // 创建下拉箭头
                const arrow = document.createElement('span');
                arrow.className = 'dropdown-arrow';
                arrow.innerHTML = '▼';

                // 创建下拉选项容器
                const dropdown = document.createElement('div');
                dropdown.className = 'multiselect-list';

                // 添加选项
                this.options.forEach(optionValue => {
                    const option = document.createElement('div');
                    option.className = 'multiselect-option';

                    const checkbox = document.createElement('input');
                    checkbox.type = 'checkbox';
                    checkbox.value = optionValue;
                    checkbox.checked = this.selectedValues.includes(optionValue);

                    const label = document.createElement('span');
                    label.textContent = optionValue;

                    // 复选框变化事件
                    checkbox.addEventListener('change', (e) => {
                        if (e.target.checked) {
                            if (!this.selectedValues.includes(optionValue)) {
                                this.selectedValues.push(optionValue);
                            }
                        } else {
                            this.selectedValues = this.selectedValues.filter(v => v !== optionValue);
                        }

                        // 更新显示值
                        input.value = this.formatDisplayValue();

                        // 更新选项样式
                        option.classList.toggle('selected', e.target.checked);
                    });

                    // 点击选项区域也能切换复选框
                    option.addEventListener('click', (e) => {
                        if (e.target !== checkbox) {
                            checkbox.checked = !checkbox.checked;
                            checkbox.dispatchEvent(new Event('change'));
                        }
                    });

                    if (checkbox.checked) {
                        option.classList.add('selected');
                    }

                    option.appendChild(checkbox);
                    option.appendChild(label);
                    dropdown.appendChild(option);
                });

                // 添加确认按钮
                const confirmBtn = document.createElement('div');
                confirmBtn.style.padding = '8px 12px';
                confirmBtn.style.backgroundColor = '#1890ff';
                confirmBtn.style.color = 'white';
                confirmBtn.style.textAlign = 'center';
                confirmBtn.style.cursor = 'pointer';
                confirmBtn.textContent = '确认选择';
                confirmBtn.addEventListener('click', () => {
                    dropdown.style.display = 'none';
                    setTimeout(() => {
                        this.successCallback();
                    }, 100);
                });
                dropdown.appendChild(confirmBtn);

                // 点击输入框或箭头显示/隐藏下拉框
                const toggleDropdown = (e) => {
                    e.stopPropagation();
                    const isVisible = dropdown.style.display === 'block';
                    dropdown.style.display = isVisible ? 'none' : 'block';
                };

                input.addEventListener('click', toggleDropdown);
                arrow.addEventListener('click', toggleDropdown);

                // 组装元素
                wrapper.appendChild(input);
                wrapper.appendChild(arrow);
                wrapper.appendChild(dropdown);

                this.element = wrapper;
                this.dropdown = dropdown;

                container.appendChild(wrapper);

                // 设置位置
                if (referencePosition?.rect) {
                    this.adjustPosition(referencePosition.rect);
                }

                // 监听滚动事件
                this.handleScroll = () => {
                    if (this.dropdown) {
                        this.dropdown.style.display = 'none';
                    }
                };

                // 监听点击外部区域
                this.handleClickOutside = (e) => {
                    if (this.element && !this.element.contains(e.target)) {
                        this.dropdown.style.display = 'none';
                    }
                };

                // 添加事件监听
                document.addEventListener('scroll', this.handleScroll, true);
                document.addEventListener('click', this.handleClickOutside);

                // 自动显示下拉框
                setTimeout(() => {
                    dropdown.style.display = 'block';
                }, 50);
            }

            parseValue(value) {
                if (!value) return [];
                if (Array.isArray(value)) return value;
                if (typeof value === 'string') {
                    return value.split(',').map(v => v.trim()).filter(v => v);
                }
                return [];
            }

            formatDisplayValue() {
                if (this.selectedValues.length === 0) return '';
                if (this.selectedValues.length === 1) return this.selectedValues[0];
                return `已选择 ${this.selectedValues.length} 项`;
            }

            adjustPosition(rect) {
                if (this.element) {
                    this.element.style.top = rect.top + 'px';
                    this.element.style.left = rect.left + 'px';
                    this.element.style.width = rect.width + 'px';
                    this.element.style.height = rect.height + 'px';
                }
            }

            getValue() {
                return this.selectedValues.join(', ');
            }

            onEnd() {
                // 移除事件监听
                document.removeEventListener('scroll', this.handleScroll, true);
                document.removeEventListener('click', this.handleClickOutside);

                // 移除DOM元素
                if (this.element && this.container && this.container.contains(this.element)) {
                    this.container.removeChild(this.element);
                }
            }

            isEditorElement(target) {
                return this.element && this.element.contains(target);
            }
        }

        // 创建不同的编辑器实例

        // 下拉框编辑器
        const statusEditor = new CustomDropdownEditor({
            values: ['待处理', '进行中', '已完成', '已取消']
        });

        const priorityEditor = new CustomDropdownEditor({
            values: ['低', '中', '高', '紧急']
        });

        const categoryEditor = new CustomDropdownEditor({
            values: ['开发', '测试', '设计', '产品', '运营']
        });

        // 复选框编辑器
        const checkboxEditor = new CheckboxEditor();

        // 日期时间编辑器
        const dateEditor = new DateTimeEditor({ type: 'date' });
        const timeEditor = new DateTimeEditor({ type: 'time' });
        const datetimeEditor = new DateTimeEditor({ type: 'datetime-local' });

        // 多选框编辑器
        const skillsEditor = new MultiSelectEditor({
            values: ['JavaScript', 'Python', 'Java', 'C++', 'React', 'Vue', 'Angular', 'Node.js']
        });

        const tagsEditor = new MultiSelectEditor({
            values: ['前端', '后端', '全栈', '移动端', 'UI/UX', '测试', '运维', '数据分析']
        });

        // 注册编辑器
        VTable.register.editor('status-editor', statusEditor);
        VTable.register.editor('priority-editor', priorityEditor);
        VTable.register.editor('category-editor', categoryEditor);
        VTable.register.editor('checkbox-editor', checkboxEditor);
        VTable.register.editor('date-editor', dateEditor);
        VTable.register.editor('time-editor', timeEditor);
        VTable.register.editor('datetime-editor', datetimeEditor);
        VTable.register.editor('skills-editor', skillsEditor);
        VTable.register.editor('tags-editor', tagsEditor);

        // 配置表格数据
        const data = [
            {
                id: 1,
                name: '任务一',
                status: '待处理',
                priority: '高',
                category: '开发',
                isCompleted: '否',
                startDate: '2024-01-15',
                startTime: '09:00',
                deadline: '2024-01-15T18:00',
                skills: 'JavaScript, React',
                tags: '前端, UI/UX',
                description: '这是第一个任务'
            },
            {
                id: 2,
                name: '任务二',
                status: '进行中',
                priority: '中',
                category: '测试',
                isCompleted: '否',
                startDate: '2024-01-16',
                startTime: '10:30',
                deadline: '2024-01-16T17:30',
                skills: 'Python, 测试',
                tags: '后端, 测试',
                description: '这是第二个任务'
            },
            {
                id: 3,
                name: '任务三',
                status: '已完成',
                priority: '低',
                category: '设计',
                isCompleted: '是',
                startDate: '2024-01-17',
                startTime: '14:00',
                deadline: '2024-01-17T16:00',
                skills: 'UI/UX, 设计',
                tags: 'UI/UX, 前端',
                description: '这是第三个任务'
            },
            {
                id: 4,
                name: '任务四',
                status: '已取消',
                priority: '紧急',
                category: '产品',
                isCompleted: '否',
                startDate: '2024-01-18',
                startTime: '08:30',
                deadline: '2024-01-18T20:00',
                skills: '产品设计, 数据分析',
                tags: '产品, 数据分析',
                description: '这是第四个任务'
            },
            {
                id: 5,
                name: '任务五',
                status: '待处理',
                priority: '中',
                category: '运营',
                isCompleted: '否',
                startDate: '2024-01-19',
                startTime: '11:00',
                deadline: '2024-01-19T15:30',
                skills: '运营, 数据分析',
                tags: '运营, 后端',
                description: '这是第五个任务'
            }
        ];

        // 配置表格列
        const columns = [
            {
                field: 'id',
                title: 'ID',
                width: 50
            },
            {
                field: 'name',
                title: '任务名称',
                width: 100
            },
            {
                field: 'status',
                title: '状态',
                width: 80,
                editor: 'status-editor'
            },
            {
                field: 'priority',
                title: '优先级',
                width: 70,
                editor: 'priority-editor'
            },
            {
                field: 'category',
                title: '分类',
                width: 70,
                editor: 'category-editor'
            },
            {
                field: 'isCompleted',
                title: '已完成',
                width: 70,
                editor: 'checkbox-editor'
            },
            {
                field: 'startDate',
                title: '开始日期',
                width: 100,
                editor: 'date-editor'
            },
            {
                field: 'startTime',
                title: '开始时间',
                width: 80,
                editor: 'time-editor'
            },
            {
                field: 'deadline',
                title: '截止时间',
                width: 130,
                editor: 'datetime-editor'
            },
            {
                field: 'skills',
                title: '技能要求',
                width: 120,
                editor: 'skills-editor'
            },
            {
                field: 'tags',
                title: '标签',
                width: 100,
                editor: 'tags-editor'
            },
            {
                field: 'description',
                title: '描述',
                width: 150
            }
        ];

        // 初始化 VTable
        const tableInstance = new VTable.ListTable({
            container: document.getElementById('tableContainer'),
            records: data,
            columns: columns,
            editCellTrigger: 'doubleclick', // 双击触发编辑
            widthMode: 'adaptive', // 自适应宽度
            heightMode: 'autoHeight', // 自适应高度
            theme: VTable.themes.DEFAULT.extends({
                headerStyle: {
                    bgColor: '#f8f9fa',
                    color: '#333',
                    fontSize: 14,
                    fontWeight: 'bold'
                },
                bodyStyle: {
                    fontSize: 13,
                    hover: {
                        cellBgColor: '#f5f5f5'
                    }
                }
            })
        });

        // 监听编辑事件
        tableInstance.on('change_cell_value', (args) => {
            console.log('单元格值已更改:', args);
        });

        // 添加一些说明文字
        const info = document.createElement('div');
        info.style.marginTop = '20px';
        info.style.padding = '15px';
        info.style.backgroundColor = '#f0f8ff';
        info.style.border = '1px solid #d0e7ff';
        info.style.borderRadius = '6px';
        info.style.lineHeight = '1.6';
        info.innerHTML = `
            <h3 style="margin-top: 0; color: #1890ff;">🎯 多种编辑器功能说明：</h3>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div>
                    <h4>📋 下拉框编辑器：</h4>
                    <ul>
                        <li><strong>状态</strong>：待处理、进行中、已完成、已取消</li>
                        <li><strong>优先级</strong>：低、中、高、紧急</li>
                        <li><strong>分类</strong>：开发、测试、设计、产品、运营</li>
                    </ul>

                    <h4>☑️ 复选框编辑器：</h4>
                    <ul>
                        <li><strong>已完成</strong>：是/否 切换</li>
                    </ul>
                </div>
                <div>
                    <h4>📅 日期时间编辑器：</h4>
                    <ul>
                        <li><strong>开始日期</strong>：日期选择器</li>
                        <li><strong>开始时间</strong>：时间选择器</li>
                        <li><strong>截止时间</strong>：日期时间选择器</li>
                    </ul>

                    <h4>🏷️ 多选框编辑器：</h4>
                    <ul>
                        <li><strong>技能要求</strong>：多项技能选择</li>
                        <li><strong>标签</strong>：多项标签选择</li>
                    </ul>
                </div>
            </div>

            <div style="margin-top: 15px; padding: 10px; background: #fff3cd; border-radius: 4px;">
                <strong>💡 使用提示：</strong>
                <ul style="margin: 5px 0;">
                    <li>双击任意可编辑单元格进入编辑模式</li>
                    <li>下拉框和多选框会在单元格内展示</li>
                    <li>滚动页面时下拉框会自动消失</li>
                    <li>点击其他区域或按ESC键关闭编辑器</li>
                    <li>复选框点击即可切换，日期时间选择器支持键盘输入</li>
                </ul>
            </div>
        `;
        document.body.appendChild(info);
    </script>
</body>

</html>